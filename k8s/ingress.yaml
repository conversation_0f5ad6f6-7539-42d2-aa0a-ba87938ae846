apiVersion: networking.k8s.io/v1
kind: Ingress
metadata:
  name: mlflow-ingress
  annotations:
    kubernetes.io/ingress.class: "nginx"
    cert-manager.io/cluster-issuer: "letsencrypt-prod"
spec:
  tls:
    - hosts:
        - mlflow.pp.ua
        - api.mlflow.pp.ua
      secretName: mlflow-pp-ua-tls
  rules:
    - host: "mlflow.pp.ua"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: oauth2-proxy-service
                port:
                  number: 4180
    - host: "api.mlflow.pp.ua"
      http:
        paths:
          - path: /
            pathType: Prefix
            backend:
              service:
                name: mlflow-tracking
                port:
                  number: 80
